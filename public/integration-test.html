<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微应用集成测试</title>
    <style>
        body { 
            margin: 0; 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            line-height: 1.6;
        }
        
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px;
        }
        
        .main-app-container { 
            margin: 20px 0; 
            padding: 20px; 
            border: 2px solid #1890ff; 
            border-radius: 8px;
            background: #fafafa;
        }
        
        .main-nav ul { 
            list-style: none; 
            padding: 0; 
            display: flex; 
            gap: 20px; 
            margin: 10px 0;
        }
        
        .main-nav a { 
            text-decoration: none; 
            color: #333; 
            padding: 8px 16px; 
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .main-nav a:hover { 
            background-color: #e6f7ff; 
        }
        
        .page-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .btn { 
            padding: 8px 16px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 0 5px;
            transition: all 0.2s;
        }
        
        .btn-primary { 
            background: #1890ff; 
            color: white; 
        }
        
        .btn-primary:hover { 
            background: #40a9ff; 
        }
        
        .btn-secondary { 
            background: #f0f0f0; 
            color: #333; 
        }
        
        .api-list { 
            background: white; 
            padding: 20px; 
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .api-item { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            padding: 12px; 
            margin: 8px 0; 
            background: #f9f9f9; 
            border-radius: 4px;
            border: 1px solid #e8e8e8;
        }
        
        .btn-edit { 
            padding: 4px 12px; 
            background: #52c41a; 
            color: white; 
            border: none; 
            border-radius: 3px; 
            cursor: pointer;
        }
        
        .control-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            z-index: 10000;
        }
        
        .control-btn {
            display: block;
            width: 100%;
            margin-bottom: 8px;
            padding: 8px 12px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .control-btn:hover {
            background: #40a9ff;
        }
        
        .control-btn.danger {
            background: #ff4d4f;
        }
        
        .control-btn.danger:hover {
            background: #ff7875;
        }
        
        .micro-app-frame {
            position: fixed;
            top: 0;
            right: 0;
            width: 400px;
            height: 100vh;
            border: none;
            z-index: 9999;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        
        .micro-app-frame.visible {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 微应用与主应用集成测试</h1>
        
        <!-- 模拟主应用的HTML结构 -->
        <div class="main-app-container">
            <h2>🏢 主应用 - API网关管理控制台</h2>
            
            <!-- 模拟导航栏 -->
            <nav class="main-nav">
                <ul>
                    <li><a href="#" data-guide-id="dashboard-menu">控制台首页</a></li>
                    <li><a href="#" data-guide-id="api-manage-menu">API管理</a></li>
                    <li><a href="#" data-guide-id="gateway-menu">网关管理</a></li>
                    <li><a href="#" data-guide-id="monitor-menu">监控中心</a></li>
                </ul>
            </nav>
            
            <!-- 模拟主要内容区域 -->
            <main class="main-content">
                <div class="page-header">
                    <h3>API管理</h3>
                    <div class="page-actions">
                        <button class="btn btn-primary" data-guide-id="create-api-button">创建API</button>
                        <button class="btn btn-secondary">批量导入</button>
                    </div>
                </div>
                
                <div class="content-area">
                    <div class="api-list">
                        <h4>现有API列表</h4>
                        <div class="api-item" data-guide-id="api-item-1">
                            <span>用户管理API</span>
                            <button class="btn-edit">编辑</button>
                        </div>
                        <div class="api-item" data-guide-id="api-item-2">
                            <span>订单查询API</span>
                            <button class="btn-edit">编辑</button>
                        </div>
                    </div>
                </div>
            </main>
        </div>
        
        <div style="margin: 40px 0; padding: 20px; background: #f6f8fa; border-radius: 8px;">
            <h3>📋 测试说明</h3>
            <ol>
                <li>点击右上角的"显示引导面板"按钮打开微应用</li>
                <li>在引导面板中点击markdown文档中的链接</li>
                <li>观察主应用中对应元素是否被正确高亮</li>
                <li>测试不同的高亮目标和样式</li>
            </ol>
        </div>
    </div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
        <button class="control-btn" onclick="showGuidePanel()">显示引导面板</button>
        <button class="control-btn" onclick="hideGuidePanel()">隐藏引导面板</button>
        <button class="control-btn danger" onclick="clearHighlight()">清除高亮</button>
    </div>
    
    <!-- 微应用iframe -->
    <iframe id="micro-app-frame" class="micro-app-frame" src="/test-guide-panel.html"></iframe>

    <script>
        let panelVisible = false;
        
        // 主应用高亮服务
        class MainAppHighlightService {
            constructor() {
                this.currentHighlight = null;
                this.addStyles();
                console.log('🎯 主应用高亮服务已初始化');
            }
            
            highlightElement(target, options = {}) {
                this.clearHighlight();
                
                const element = this.findElement(target);
                if (!element) {
                    console.error('❌ 未找到目标元素:', target);
                    return;
                }
                
                const defaultOptions = {
                    padding: 8,
                    borderRadius: 4,
                    borderColor: '#1890ff',
                    borderWidth: 2,
                    animation: 'pulse',
                    ...options
                };
                
                const rect = element.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
                
                // 创建高亮遮罩
                const highlight = document.createElement('div');
                highlight.id = 'guide-highlight-overlay';
                highlight.style.cssText = 
                    'position: absolute;' +
                    'top: ' + (rect.top + scrollTop - defaultOptions.padding) + 'px;' +
                    'left: ' + (rect.left + scrollLeft - defaultOptions.padding) + 'px;' +
                    'width: ' + (rect.width + defaultOptions.padding * 2) + 'px;' +
                    'height: ' + (rect.height + defaultOptions.padding * 2) + 'px;' +
                    'border: ' + defaultOptions.borderWidth + 'px solid ' + defaultOptions.borderColor + ';' +
                    'border-radius: ' + defaultOptions.borderRadius + 'px;' +
                    'pointer-events: none;' +
                    'z-index: 999999;' +
                    'box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3);' +
                    'animation: ' + (defaultOptions.animation === 'pulse' ? 'guide-pulse 2s infinite' : 'none') + ';';
                
                document.body.appendChild(highlight);
                this.currentHighlight = highlight;
                
                // 滚动到目标元素
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                
                console.log('✅ 高亮元素:', element.textContent || element.tagName);
            }
            
            clearHighlight() {
                if (this.currentHighlight) {
                    this.currentHighlight.remove();
                    this.currentHighlight = null;
                    console.log('🧹 高亮已清除');
                }
            }
            
            findElement(target) {
                // 策略1: CSS选择器
                if (target.selector) {
                    return document.querySelector(target.selector);
                }
                
                // 策略2: data-guide-id
                if (target.guideId) {
                    return document.querySelector('[data-guide-id="' + target.guideId + '"]');
                }
                
                // 策略3: 文本匹配
                if (target.text) {
                    const elements = Array.from(document.querySelectorAll('button, a, [role="button"]'))
                        .filter(el => el.textContent && el.textContent.trim().includes(target.text));
                    return elements[0] || null;
                }
                
                return null;
            }
            
            addStyles() {
                if (!document.getElementById('guide-highlight-styles')) {
                    const style = document.createElement('style');
                    style.id = 'guide-highlight-styles';
                    style.textContent = 
                        '@keyframes guide-pulse {' +
                        '0% { opacity: 1; transform: scale(1); }' +
                        '50% { opacity: 0.8; transform: scale(1.02); }' +
                        '100% { opacity: 1; transform: scale(1); }' +
                        '}';
                    document.head.appendChild(style);
                }
            }
        }
        
        // 初始化服务
        window.guideHighlightService = new MainAppHighlightService();
        
        // 监听来自微应用的消息
        window.addEventListener('message', function(event) {
            const { type, target, options } = event.data;
            
            console.log('📨 收到消息:', event.data);
            
            switch (type) {
                case 'GUIDE_HIGHLIGHT':
                    console.log('📨 收到高亮请求:', target);
                    window.guideHighlightService.highlightElement(target, options);
                    // 回复成功消息
                    event.source.postMessage({
                        type: 'HIGHLIGHT_RESULT',
                        data: { success: true, target: target, options: options }
                    }, '*');
                    break;
                    
                case 'GUIDE_CLEAR_HIGHLIGHT':
                    console.log('📨 收到清除高亮请求');
                    window.guideHighlightService.clearHighlight();
                    // 回复成功消息
                    event.source.postMessage({
                        type: 'CLEAR_HIGHLIGHT_RESULT',
                        data: { success: true }
                    }, '*');
                    break;
                    
                default:
                    // 其他消息类型的处理
                    if (type && type.startsWith('GUIDE_')) {
                        console.log('📨 收到引导消息:', type);
                    }
            }
        });
        
        // 显示引导面板
        function showGuidePanel() {
            const frame = document.getElementById('micro-app-frame');
            frame.classList.add('visible');
            panelVisible = true;
        }
        
        // 隐藏引导面板
        function hideGuidePanel() {
            const frame = document.getElementById('micro-app-frame');
            frame.classList.remove('visible');
            panelVisible = false;
        }
        
        // 清除高亮
        function clearHighlight() {
            window.guideHighlightService.clearHighlight();
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            console.log('🎯 集成测试页面加载完成，高亮服务已就绪');
        });
    </script>
</body>
</html>
