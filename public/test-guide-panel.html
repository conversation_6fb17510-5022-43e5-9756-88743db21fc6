<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>引导面板测试</title>
    <style>
        body { 
            margin: 0; 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
        }
        
        .guide-panel {
            position: fixed;
            top: 0;
            right: 0;
            width: 400px;
            height: 100vh;
            background: white;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
            z-index: 10001;
            display: flex;
            flex-direction: column;
        }
        
        .guide-header {
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
            background: #f6f8fa;
        }
        
        .guide-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .guide-content h1 {
            margin-top: 0;
            color: #1f2328;
            font-size: 24px;
        }
        
        .guide-content h2 {
            color: #1f2328;
            font-size: 18px;
            margin-top: 24px;
            margin-bottom: 12px;
        }
        
        .guide-content h3 {
            color: #1f2328;
            font-size: 16px;
            margin-top: 20px;
            margin-bottom: 8px;
        }
        
        .guide-content p {
            line-height: 1.6;
            margin-bottom: 12px;
        }
        
        .guide-content ul, .guide-content ol {
            padding-left: 20px;
            margin-bottom: 12px;
        }
        
        .guide-content li {
            margin-bottom: 4px;
        }
        
        .guide-content a {
            color: #1890ff;
            text-decoration: underline;
            cursor: pointer;
        }
        
        .guide-content a:hover {
            color: #40a9ff;
        }
        
        .guide-content code {
            background: #f6f8fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
        }
        
        .guide-content blockquote {
            border-left: 4px solid #d1d9e0;
            padding-left: 16px;
            margin: 16px 0;
            color: #656d76;
        }
        
        .guide-footer {
            padding: 20px;
            border-top: 1px solid #e1e5e9;
            background: #f6f8fa;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 8px;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
        }
        
        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }
        
        .btn-secondary:hover {
            background: #e0e0e0;
        }
        
        .toggle-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10002;
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <button class="toggle-btn" onclick="togglePanel()">显示引导</button>
    
    <div id="guide-panel" class="guide-panel" style="transform: translateX(100%);">
        <div class="guide-header">
            <h3>📖 API创建指南</h3>
        </div>
        <div class="guide-content" id="guide-content">
            加载中...
        </div>
        <div class="guide-footer">
            <button class="btn btn-secondary" onclick="togglePanel()">关闭</button>
            <button class="btn btn-primary" onclick="testHighlight()">测试高亮</button>
        </div>
    </div>

    <!-- 引入 marked.js 用于 markdown 解析 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <script>
        let panelVisible = false;
        
        // 切换面板显示
        function togglePanel() {
            const panel = document.getElementById('guide-panel');
            panelVisible = !panelVisible;
            
            if (panelVisible) {
                panel.style.transform = 'translateX(0)';
                loadGuideContent();
            } else {
                panel.style.transform = 'translateX(100%)';
            }
        }
        
        // 加载引导内容
        async function loadGuideContent() {
            try {
                const response = await fetch('/guides/api-creation-guide.md');
                const markdown = await response.text();
                
                // 解析 markdown
                const html = marked.parse(markdown);
                
                // 处理特殊链接
                const processedHtml = processGuideLinks(html);
                
                document.getElementById('guide-content').innerHTML = processedHtml;
                
                // 绑定链接点击事件
                bindLinkEvents();
                
            } catch (error) {
                console.error('加载引导内容失败:', error);
                document.getElementById('guide-content').innerHTML = '加载失败，请稍后重试。';
            }
        }
        
        // 处理引导链接
        function processGuideLinks(html) {
            // 替换 guide:highlight: 链接
            return html.replace(
                /href="guide:highlight:([^"]+)"/g, 
                'href="#" data-guide-action="highlight" data-guide-target="$1"'
            );
        }
        
        // 绑定链接点击事件
        function bindLinkEvents() {
            const links = document.querySelectorAll('a[data-guide-action]');
            
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const action = this.getAttribute('data-guide-action');
                    const target = this.getAttribute('data-guide-target');
                    
                    console.log('点击引导链接:', { action, target });
                    
                    if (action === 'highlight') {
                        highlightElement(target);
                    }
                });
            });
        }
        
        // 高亮元素
        function highlightElement(targetId) {
            console.log('高亮目标:', targetId);
            
            // 向父窗口发送高亮消息
            if (window.parent !== window) {
                window.parent.postMessage({
                    type: 'GUIDE_HIGHLIGHT',
                    target: { guideId: targetId },
                    options: { 
                        borderColor: '#1890ff', 
                        borderWidth: 3,
                        animation: 'pulse'
                    }
                }, '*');
            } else {
                // 如果在同一窗口，直接调用高亮服务
                if (window.guideHighlightService) {
                    window.guideHighlightService.highlightElement({
                        guideId: targetId
                    }, {
                        borderColor: '#1890ff',
                        borderWidth: 3,
                        animation: 'pulse'
                    });
                } else {
                    console.warn('高亮服务未找到');
                }
            }
        }
        
        // 测试高亮功能
        function testHighlight() {
            highlightElement('create-api-button');
        }
        
        // 监听来自父窗口的消息
        window.addEventListener('message', function(event) {
            const { type, data } = event.data;
            
            switch (type) {
                case 'HIGHLIGHT_RESULT':
                    console.log('高亮结果:', data);
                    break;
                case 'CLEAR_HIGHLIGHT_RESULT':
                    console.log('清除高亮结果:', data);
                    break;
            }
        });
        
        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            console.log('引导面板页面加载完成');
        });
    </script>
</body>
</html>
