<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Title</title>
  <style>
    body { margin: 20px; font-family: Arial, sans-serif; }
    .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
    button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    .test-target { padding: 15px; margin: 10px; background: #f5f5f5; border: 1px solid #ccc; }
</style>
  <% if (__dev__) { %>
    <!-- 以下只在开发环境生效，千万不要写出 这个 if 判断，线上具体配置以 Viper 上的配置为主-->
    <script>
      var ALIYUN_CONSOLE_CONFIG = {
        LANG: 'zh',
        LOCALE: 'zh_cn',
        portalType: 'one',
        SEC_TOKEN: 'mock-sec-token',
        CHANNEL_FEATURE_STATUS: {},
        CHANNEL_LINKS: {},
        REGIONS: [],
        FEATURE_STATUS: {}
      };
      var RISK_INFO = {
        GETUA: function () {
          return 'mock-collina-ua'
        },
        UMID: 'mock-umid'
      };
    </script>
  <% } %>
</head>
<body>
  <div id="app"></div>
  <h1>🎯 智能高亮系统测试</h1>
    
  <div class="test-section">
      <h2>测试目标元素</h2>
      <div class="test-target" data-guide-id="test-element-1">
          测试元素 1 (data-guide-id="test-element-1")
      </div>
      <div class="test-target" id="test-element-2">
          测试元素 2 (id="test-element-2")
      </div>
      <button class="test-target">创建API</button>
      <a href="#" class="test-target">API管理</a>
  </div>
  
  <div class="test-section">
      <h2>测试控制</h2>
      <button onclick="testGuideId()">测试 data-guide-id</button>
      <button onclick="testSelector()">测试 CSS 选择器</button>
      <button onclick="testText()">测试文本匹配</button>
      <button onclick="clearHighlight()">清除高亮</button>
  </div>
  
  <div class="test-section">
      <h2>测试结果</h2>
      <div id="test-results" style="background: #f0f0f0; padding: 10px; min-height: 100px;">
          等待测试...
      </div>
  </div>
  <script>
    function log(message) {
        const results = document.getElementById('test-results');
        results.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
        results.scrollTop = results.scrollHeight;
    }
    
    function testGuideId() {
        log('测试 data-guide-id 高亮...');
        if (window.guideHighlightService) {
            window.guideHighlightService.highlightElement({
                guideId: 'test-element-1'
            }, {
                borderColor: '#52c41a',
                borderWidth: 3
            });
            log('✅ data-guide-id 测试执行完成');
        } else {
            log('❌ 高亮服务未初始化');
        }
    }
    
    function testSelector() {
        log('测试 CSS 选择器高亮...');
        if (window.guideHighlightService) {
            window.guideHighlightService.highlightElement({
                selector: '#test-element-2'
            }, {
                borderColor: '#1890ff',
                borderWidth: 3
            });
            log('✅ CSS 选择器测试执行完成');
        } else {
            log('❌ 高亮服务未初始化');
        }
    }
    
    function testText() {
        log('测试文本匹配高亮...');
        if (window.guideHighlightService) {
            window.guideHighlightService.highlightElement({
                text: '创建API'
            }, {
                borderColor: '#faad14',
                borderWidth: 3
            });
            log('✅ 文本匹配测试执行完成');
        } else {
            log('❌ 高亮服务未初始化');
        }
    }
    
    function clearHighlight() {
        log('清除高亮...');
        if (window.guideHighlightService) {
            window.guideHighlightService.clearHighlight();
            log('🧹 高亮已清除');
        } else {
            log('❌ 高亮服务未初始化');
        }
    }
    
    // 页面加载完成后检查服务
    window.addEventListener('load', function() {
        if (window.guideHighlightService) {
            log('🎯 高亮服务已就绪');
        } else {
            log('⚠️ 高亮服务未找到，请确保在主页面中运行');
        }
    });
</script>
  <!-- 
    <script>
      // window.CONSOLE_BASE_SETTINGS = {}
      // 详细配置参见： https://yuque.antfin-inc.com/console/console-base/conf-settings
    </script>
  -->

</body>
</html>
