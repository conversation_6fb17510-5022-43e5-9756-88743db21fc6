<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Title</title>
  <style>
    body { margin: 20px; font-family: Arial, sans-serif; }
    .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
    button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    .test-target { padding: 15px; margin: 10px; background: #f5f5f5; border: 1px solid #ccc; }

    /* 主应用样式 */
    .main-app-container {
      margin: 20px 0;
      padding: 20px;
      border: 2px solid #1890ff;
      border-radius: 8px;
      background: #fafafa;
    }
    .main-nav ul {
      list-style: none;
      padding: 0;
      display: flex;
      gap: 20px;
      margin: 10px 0;
    }
    .main-nav a {
      text-decoration: none;
      color: #333;
      padding: 8px 16px;
      border-radius: 4px;
      transition: background-color 0.2s;
    }
    .main-nav a:hover {
      background-color: #e6f7ff;
    }
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 20px 0;
      padding: 15px;
      background: white;
      border-radius: 6px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin: 0 5px;
      transition: all 0.2s;
    }
    .btn-primary {
      background: #1890ff;
      color: white;
    }
    .btn-primary:hover {
      background: #40a9ff;
    }
    .btn-secondary {
      background: #f0f0f0;
      color: #333;
    }
    .api-list {
      background: white;
      padding: 20px;
      border-radius: 6px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .api-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      margin: 8px 0;
      background: #f9f9f9;
      border-radius: 4px;
      border: 1px solid #e8e8e8;
    }
    .btn-edit {
      padding: 4px 12px;
      background: #52c41a;
      color: white;
      border: none;
      border-radius: 3px;
      cursor: pointer;
    }
</style>
  <% if (__dev__) { %>
    <!-- 以下只在开发环境生效，千万不要写出 这个 if 判断，线上具体配置以 Viper 上的配置为主-->
    <script>
      var ALIYUN_CONSOLE_CONFIG = {
        LANG: 'zh',
        LOCALE: 'zh_cn',
        portalType: 'one',
        SEC_TOKEN: 'mock-sec-token',
        CHANNEL_FEATURE_STATUS: {},
        CHANNEL_LINKS: {},
        REGIONS: [],
        FEATURE_STATUS: {}
      };
      var RISK_INFO = {
        GETUA: function () {
          return 'mock-collina-ua'
        },
        UMID: 'mock-umid'
      };
    </script>
  <% } %>
</head>
<body>
  <div id="app"></div>

  <!-- 模拟主应用的HTML结构 -->
  <div class="main-app-container">
    <h1>🎯 主应用 - API网关管理控制台</h1>

    <!-- 模拟导航栏 -->
    <nav class="main-nav">
      <ul>
        <li><a href="#" data-guide-id="dashboard-menu">控制台首页</a></li>
        <li><a href="#" data-guide-id="api-manage-menu">API管理</a></li>
        <li><a href="#" data-guide-id="gateway-menu">网关管理</a></li>
        <li><a href="#" data-guide-id="monitor-menu">监控中心</a></li>
      </ul>
    </nav>

    <!-- 模拟主要内容区域 -->
    <main class="main-content">
      <div class="page-header">
        <h2>API管理</h2>
        <div class="page-actions">
          <button class="btn btn-primary" data-guide-id="create-api-button">创建API</button>
          <button class="btn btn-secondary">批量导入</button>
        </div>
      </div>

      <div class="content-area">
        <div class="api-list">
          <h3>现有API列表</h3>
          <div class="api-item" data-guide-id="api-item-1">
            <span>用户管理API</span>
            <button class="btn-edit">编辑</button>
          </div>
          <div class="api-item" data-guide-id="api-item-2">
            <span>订单查询API</span>
            <button class="btn-edit">编辑</button>
          </div>
        </div>
      </div>
    </main>
  </div>

  <h1>🎯 智能高亮系统测试</h1>

  <div class="test-section">
      <h2>测试目标元素</h2>
      <div class="test-target" data-guide-id="test-element-1">
          测试元素 1 (data-guide-id="test-element-1")
      </div>
      <div class="test-target" id="test-element-2">
          测试元素 2 (id="test-element-2")
      </div>
      <button class="test-target">创建API</button>
      <a href="#" class="test-target">API管理</a>
  </div>
  
  <div class="test-section">
      <h2>测试控制</h2>
      <button onclick="testGuideId()">测试 data-guide-id</button>
      <button onclick="testSelector()">测试 CSS 选择器</button>
      <button onclick="testText()">测试文本匹配</button>
      <button onclick="clearHighlight()">清除高亮</button>
  </div>
  
  <div class="test-section">
      <h2>测试结果</h2>
      <div id="test-results" style="background: #f0f0f0; padding: 10px; min-height: 100px;">
          等待测试...
      </div>
  </div>
  <script>
    function log(message) {
        const results = document.getElementById('test-results');
        results.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
        results.scrollTop = results.scrollHeight;
    }
    
    function testGuideId() {
        log('测试 data-guide-id 高亮...');
        if (window.guideHighlightService) {
            window.guideHighlightService.highlightElement({
                guideId: 'test-element-1'
            }, {
                borderColor: '#52c41a',
                borderWidth: 3
            });
            log('✅ data-guide-id 测试执行完成');
        } else {
            log('❌ 高亮服务未初始化');
        }
    }
    
    function testSelector() {
        log('测试 CSS 选择器高亮...');
        if (window.guideHighlightService) {
            window.guideHighlightService.highlightElement({
                selector: '#test-element-2'
            }, {
                borderColor: '#1890ff',
                borderWidth: 3
            });
            log('✅ CSS 选择器测试执行完成');
        } else {
            log('❌ 高亮服务未初始化');
        }
    }
    
    function testText() {
        log('测试文本匹配高亮...');
        if (window.guideHighlightService) {
            window.guideHighlightService.highlightElement({
                text: '创建API'
            }, {
                borderColor: '#faad14',
                borderWidth: 3
            });
            log('✅ 文本匹配测试执行完成');
        } else {
            log('❌ 高亮服务未初始化');
        }
    }
    
    function clearHighlight() {
        log('清除高亮...');
        if (window.guideHighlightService) {
            window.guideHighlightService.clearHighlight();
            log('🧹 高亮已清除');
        } else {
            log('❌ 高亮服务未初始化');
        }
    }
    
    // 页面加载完成后检查服务
    window.addEventListener('load', function() {
        if (window.guideHighlightService) {
            log('🎯 高亮服务已就绪');
        } else {
            log('⚠️ 高亮服务未找到，请确保在主页面中运行');
        }
    });

    // 简化的高亮服务，避免模板字符串问题
    function initMainAppHighlightService() {
        window.guideHighlightService = {
            currentHighlight: null,

            highlightElement: function(target, options) {
                this.clearHighlight();

                var element = this.findElement(target);
                if (!element) {
                    log('❌ 未找到目标元素: ' + JSON.stringify(target));
                    return;
                }

                var rect = element.getBoundingClientRect();
                var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                var scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
                var padding = (options && options.padding) || 8;
                var borderColor = (options && options.borderColor) || '#1890ff';
                var borderWidth = (options && options.borderWidth) || 2;

                var highlight = document.createElement('div');
                highlight.id = 'guide-highlight-overlay';
                highlight.style.position = 'absolute';
                highlight.style.top = (rect.top + scrollTop - padding) + 'px';
                highlight.style.left = (rect.left + scrollLeft - padding) + 'px';
                highlight.style.width = (rect.width + padding * 2) + 'px';
                highlight.style.height = (rect.height + padding * 2) + 'px';
                highlight.style.border = borderWidth + 'px solid ' + borderColor;
                highlight.style.borderRadius = '4px';
                highlight.style.pointerEvents = 'none';
                highlight.style.zIndex = '999999';
                highlight.style.boxShadow = '0 0 0 9999px rgba(0, 0, 0, 0.3)';
                highlight.style.animation = 'guide-pulse 2s infinite';

                document.body.appendChild(highlight);
                this.currentHighlight = highlight;

                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                log('✅ 高亮元素: ' + (element.textContent || element.tagName).substring(0, 30));
            },

            clearHighlight: function() {
                if (this.currentHighlight) {
                    this.currentHighlight.remove();
                    this.currentHighlight = null;
                    log('🧹 高亮已清除');
                }
            },

            findElement: function(target) {
                if (target.selector) {
                    return document.querySelector(target.selector);
                }
                if (target.guideId) {
                    return document.querySelector('[data-guide-id="' + target.guideId + '"]');
                }
                if (target.text) {
                    var elements = Array.from(document.querySelectorAll('button, a, [role="button"]'))
                        .filter(function(el) {
                            return el.textContent && el.textContent.trim().includes(target.text);
                        });
                    return elements[0] || null;
                }
                return null;
            }
        };

        // 添加样式
        if (!document.getElementById('guide-highlight-styles')) {
            var style = document.createElement('style');
            style.id = 'guide-highlight-styles';
            style.textContent = '@keyframes guide-pulse { 0% { opacity: 1; transform: scale(1); } 50% { opacity: 0.8; transform: scale(1.02); } 100% { opacity: 1; transform: scale(1); } }';
            document.head.appendChild(style);
        }

        log('🎯 主应用高亮服务已初始化');

        // 监听来自微应用的消息
        window.addEventListener('message', function(event) {
            var data = event.data;
            var type = data.type;
            var target = data.target;
            var options = data.options;

            switch (type) {
                case 'GUIDE_HIGHLIGHT':
                    log('📨 收到高亮请求: ' + JSON.stringify(target));
                    window.guideHighlightService.highlightElement(target, options);
                    if (event.source) {
                        event.source.postMessage({
                            type: 'HIGHLIGHT_RESULT',
                            data: { success: true, target: target, options: options }
                        }, '*');
                    }
                    break;

                case 'GUIDE_CLEAR_HIGHLIGHT':
                    log('📨 收到清除高亮请求');
                    window.guideHighlightService.clearHighlight();
                    if (event.source) {
                        event.source.postMessage({
                            type: 'CLEAR_HIGHLIGHT_RESULT',
                            data: { success: true }
                        }, '*');
                    }
                    break;

                default:
                    if (type && type.indexOf('GUIDE_') === 0) {
                        log('📨 收到引导消息: ' + type);
                    }
            }
        });
    }

    // 立即初始化主应用高亮服务
    initMainAppHighlightService();
</script>
  <!-- 
    <script>
      // window.CONSOLE_BASE_SETTINGS = {}
      // 详细配置参见： https://yuque.antfin-inc.com/console/console-base/conf-settings
    </script>
  -->

</body>
</html>
