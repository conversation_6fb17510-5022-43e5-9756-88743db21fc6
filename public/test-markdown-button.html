<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown按钮测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            gap: 20px;
        }
        .main-content {
            flex: 1;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .guide-panel {
            width: 400px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: fit-content;
        }
        .test-target {
            padding: 10px 20px;
            margin: 10px;
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 4px;
            cursor: pointer;
            display: inline-block;
        }
        .test-target:hover {
            background: #bbdefb;
        }
        h1, h2 {
            color: #333;
        }
        .markdown-content {
            border: 1px solid #ddd;
            padding: 15px;
            background: #fafafa;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="main-content">
            <h1>主应用内容</h1>
            <p>这里是主应用的内容，包含一些可以被高亮的元素：</p>
            
            <button data-guide-id="create-api-button" class="test-target">创建API</button>
            <button data-guide-id="api-manage-menu" class="test-target">API管理</button>
            <button data-guide-id="dashboard-menu" class="test-target">控制台首页</button>
            <button data-guide-id="gateway-menu" class="test-target">网关管理</button>
            <button data-guide-id="monitor-menu" class="test-target">监控中心</button>
            <button class="test-target">文本高亮</button>
            
            <h2>测试按钮</h2>
            <button onclick="testMarkdownButton()">测试Markdown按钮解析</button>
            <button onclick="clearHighlight()">清除高亮</button>
        </div>
        
        <div class="guide-panel">
            <h2>引导面板</h2>
            <div id="guide-content">
                <p>点击"测试Markdown按钮解析"来查看markdown内容的解析结果</p>
            </div>
        </div>
    </div>

    <!-- 引入微应用 -->
    <script src="/index.js"></script>
    
    <script>
        // 主应用高亮服务
        class MainAppHighlightService {
            constructor() {
                this.currentHighlight = null;
                this.setupMessageListener();
            }

            setupMessageListener() {
                window.addEventListener('message', (event) => {
                    if (event.data && event.data.type === 'GUIDE_HIGHLIGHT') {
                        console.log('🎯 收到高亮请求:', event.data);
                        this.highlightElement(event.data.target, event.data.options);
                    } else if (event.data && event.data.type === 'GUIDE_CLEAR_HIGHLIGHT') {
                        console.log('🧹 收到清除高亮请求');
                        this.clearHighlight();
                    }
                });
            }

            highlightElement(target, options = {}) {
                this.clearHighlight();
                
                let element = null;
                
                if (target.guideId) {
                    element = document.querySelector(`[data-guide-id="${target.guideId}"]`);
                } else if (target.selector) {
                    element = document.querySelector(target.selector);
                } else if (target.text) {
                    const elements = Array.from(document.querySelectorAll('*'));
                    element = elements.find(el => 
                        el.textContent && el.textContent.trim() === target.text.trim()
                    );
                }
                
                if (element) {
                    console.log('✅ 找到目标元素:', element);
                    this.createHighlightOverlay(element, options);
                } else {
                    console.warn('❌ 未找到目标元素:', target);
                }
            }

            createHighlightOverlay(element, options) {
                const rect = element.getBoundingClientRect();
                const overlay = document.createElement('div');
                
                overlay.style.cssText = `
                    position: fixed;
                    top: ${rect.top - (options.padding || 4)}px;
                    left: ${rect.left - (options.padding || 4)}px;
                    width: ${rect.width + (options.padding || 4) * 2}px;
                    height: ${rect.height + (options.padding || 4) * 2}px;
                    border: ${options.borderWidth || 3}px solid ${options.borderColor || '#1890ff'};
                    border-radius: 4px;
                    pointer-events: none;
                    z-index: 10000;
                    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                    animation: ${options.animation || 'pulse'} 2s infinite;
                `;
                
                document.body.appendChild(overlay);
                this.currentHighlight = overlay;
            }

            clearHighlight() {
                if (this.currentHighlight) {
                    this.currentHighlight.remove();
                    this.currentHighlight = null;
                }
            }
        }

        // 初始化高亮服务
        const highlightService = new MainAppHighlightService();

        // 测试函数
        function testMarkdownButton() {
            const markdownContent = `
# 测试Markdown按钮

这是一个测试页面，用于验证markdown中的自定义按钮是否能正常工作。

<guide-button 
  action="highlight" 
  type="highlight" 
  text="🎯 高亮创建API按钮" 
  highlight-target="create-api-button"
/>

<guide-button 
  action="highlight" 
  type="highlight" 
  text="🎯 高亮API管理菜单" 
  highlight-target="api-manage-menu"
/>

<guide-button 
  action="clearHighlight" 
  type="secondary" 
  text="🧹 清除高亮" 
/>

## 测试链接方式

您也可以通过链接方式：[API管理](guide:highlight:api-manage-menu) 菜单。
            `;
            
            // 渲染markdown内容
            if (window.MarkdownRenderer) {
                const renderer = new window.MarkdownRenderer();
                const htmlContent = renderer.render(markdownContent);
                document.getElementById('guide-content').innerHTML = htmlContent;
            } else {
                document.getElementById('guide-content').innerHTML = '<p>MarkdownRenderer未加载</p>';
            }
        }

        function clearHighlight() {
            highlightService.clearHighlight();
        }

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
