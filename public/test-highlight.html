<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高亮功能测试页面</title>
    <style>
        body { 
            margin: 20px; 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            line-height: 1.6;
        }
        
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
        }
        
        .section { 
            margin: 30px 0; 
            padding: 20px; 
            border: 1px solid #e1e5e9; 
            border-radius: 8px;
            background: #fff;
        }
        
        .main-app-container { 
            margin: 20px 0; 
            padding: 20px; 
            border: 2px solid #1890ff; 
            border-radius: 8px;
            background: #fafafa;
        }
        
        .main-nav ul { 
            list-style: none; 
            padding: 0; 
            display: flex; 
            gap: 20px; 
            margin: 10px 0;
        }
        
        .main-nav a { 
            text-decoration: none; 
            color: #333; 
            padding: 8px 16px; 
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .main-nav a:hover { 
            background-color: #e6f7ff; 
        }
        
        .page-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .btn { 
            padding: 8px 16px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 0 5px;
            transition: all 0.2s;
        }
        
        .btn-primary { 
            background: #1890ff; 
            color: white; 
        }
        
        .btn-primary:hover { 
            background: #40a9ff; 
        }
        
        .btn-secondary { 
            background: #f0f0f0; 
            color: #333; 
        }
        
        .api-list { 
            background: white; 
            padding: 20px; 
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .api-item { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            padding: 12px; 
            margin: 8px 0; 
            background: #f9f9f9; 
            border-radius: 4px;
            border: 1px solid #e8e8e8;
        }
        
        .btn-edit { 
            padding: 4px 12px; 
            background: #52c41a; 
            color: white; 
            border: none; 
            border-radius: 3px; 
            cursor: pointer;
        }
        
        .test-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .test-btn {
            padding: 10px 15px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .test-btn:hover {
            background: #40a9ff;
        }
        
        .test-btn.clear {
            background: #ff4d4f;
        }
        
        .test-btn.clear:hover {
            background: #ff7875;
        }
        
        .test-results {
            background: #f6f8fa;
            border: 1px solid #d1d9e0;
            border-radius: 6px;
            padding: 15px;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .guide-panel {
            position: fixed;
            top: 0;
            right: 0;
            width: 400px;
            height: 100vh;
            background: white;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
            z-index: 10001;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        
        .guide-panel.visible {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 微应用高亮功能测试</h1>
        
        <!-- 模拟主应用的HTML结构 -->
        <div class="main-app-container">
            <h2>🏢 主应用 - API网关管理控制台</h2>
            
            <!-- 模拟导航栏 -->
            <nav class="main-nav">
                <ul>
                    <li><a href="#" data-guide-id="dashboard-menu">控制台首页</a></li>
                    <li><a href="#" data-guide-id="api-manage-menu">API管理</a></li>
                    <li><a href="#" data-guide-id="gateway-menu">网关管理</a></li>
                    <li><a href="#" data-guide-id="monitor-menu">监控中心</a></li>
                </ul>
            </nav>
            
            <!-- 模拟主要内容区域 -->
            <main class="main-content">
                <div class="page-header">
                    <h3>API管理</h3>
                    <div class="page-actions">
                        <button class="btn btn-primary" data-guide-id="create-api-button">创建API</button>
                        <button class="btn btn-secondary">批量导入</button>
                    </div>
                </div>
                
                <div class="content-area">
                    <div class="api-list">
                        <h4>现有API列表</h4>
                        <div class="api-item" data-guide-id="api-item-1">
                            <span>用户管理API</span>
                            <button class="btn-edit">编辑</button>
                        </div>
                        <div class="api-item" data-guide-id="api-item-2">
                            <span>订单查询API</span>
                            <button class="btn-edit">编辑</button>
                        </div>
                    </div>
                </div>
            </main>
        </div>
        
        <!-- 测试控制区域 -->
        <div class="section">
            <h2>🧪 测试控制</h2>
            <div class="test-controls">
                <button class="test-btn" onclick="testGuideId()">测试 data-guide-id</button>
                <button class="test-btn" onclick="testSelector()">测试 CSS 选择器</button>
                <button class="test-btn" onclick="testText()">测试文本匹配</button>
                <button class="test-btn" onclick="testMarkdownLink()">测试 Markdown 链接</button>
                <button class="test-btn clear" onclick="clearHighlight()">清除高亮</button>
            </div>
        </div>
        
        <!-- 测试结果显示 -->
        <div class="section">
            <h2>📊 测试结果</h2>
            <div id="test-results" class="test-results">
                等待测试...
            </div>
        </div>
        
        <!-- 微应用容器 -->
        <div id="app"></div>
    </div>

    <script>
        // 日志函数
        function log(message) {
            const results = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            results.innerHTML += timestamp + ': ' + message + '<br>';
            results.scrollTop = results.scrollHeight;
        }
        
        // 主应用高亮服务
        class MainAppHighlightService {
            constructor() {
                this.currentHighlight = null;
                this.addStyles();
                log('🎯 主应用高亮服务已初始化');
            }
            
            highlightElement(target, options = {}) {
                this.clearHighlight();
                
                const element = this.findElement(target);
                if (!element) {
                    log('❌ 未找到目标元素: ' + JSON.stringify(target));
                    return;
                }
                
                const defaultOptions = {
                    padding: 8,
                    borderRadius: 4,
                    borderColor: '#1890ff',
                    borderWidth: 2,
                    animation: 'pulse',
                    ...options
                };
                
                const rect = element.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
                
                // 创建高亮遮罩
                const highlight = document.createElement('div');
                highlight.id = 'guide-highlight-overlay';
                highlight.style.cssText = 
                    'position: absolute;' +
                    'top: ' + (rect.top + scrollTop - defaultOptions.padding) + 'px;' +
                    'left: ' + (rect.left + scrollLeft - defaultOptions.padding) + 'px;' +
                    'width: ' + (rect.width + defaultOptions.padding * 2) + 'px;' +
                    'height: ' + (rect.height + defaultOptions.padding * 2) + 'px;' +
                    'border: ' + defaultOptions.borderWidth + 'px solid ' + defaultOptions.borderColor + ';' +
                    'border-radius: ' + defaultOptions.borderRadius + 'px;' +
                    'pointer-events: none;' +
                    'z-index: 999999;' +
                    'box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3);' +
                    'animation: ' + (defaultOptions.animation === 'pulse' ? 'guide-pulse 2s infinite' : 'none') + ';';
                
                document.body.appendChild(highlight);
                this.currentHighlight = highlight;
                
                // 滚动到目标元素
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                
                log('✅ 高亮元素: ' + (element.textContent || element.tagName).substring(0, 30));
            }
            
            clearHighlight() {
                if (this.currentHighlight) {
                    this.currentHighlight.remove();
                    this.currentHighlight = null;
                    log('🧹 高亮已清除');
                }
            }
            
            findElement(target) {
                // 策略1: CSS选择器
                if (target.selector) {
                    return document.querySelector(target.selector);
                }
                
                // 策略2: data-guide-id
                if (target.guideId) {
                    return document.querySelector('[data-guide-id="' + target.guideId + '"]');
                }
                
                // 策略3: 文本匹配
                if (target.text) {
                    const elements = Array.from(document.querySelectorAll('button, a, [role="button"]'))
                        .filter(el => el.textContent && el.textContent.trim().includes(target.text));
                    return elements[0] || null;
                }
                
                return null;
            }
            
            addStyles() {
                if (!document.getElementById('guide-highlight-styles')) {
                    const style = document.createElement('style');
                    style.id = 'guide-highlight-styles';
                    style.textContent = 
                        '@keyframes guide-pulse {' +
                        '0% { opacity: 1; transform: scale(1); }' +
                        '50% { opacity: 0.8; transform: scale(1.02); }' +
                        '100% { opacity: 1; transform: scale(1); }' +
                        '}';
                    document.head.appendChild(style);
                }
            }
        }
        
        // 初始化服务
        window.guideHighlightService = new MainAppHighlightService();
        
        // 监听来自微应用的消息
        window.addEventListener('message', function(event) {
            const { type, target, options } = event.data;
            
            switch (type) {
                case 'GUIDE_HIGHLIGHT':
                    log('📨 收到高亮请求: ' + JSON.stringify(target));
                    window.guideHighlightService.highlightElement(target, options);
                    // 回复成功消息
                    if (event.source) {
                        event.source.postMessage({
                            type: 'HIGHLIGHT_RESULT',
                            data: { success: true, target: target, options: options }
                        }, '*');
                    }
                    break;
                    
                case 'GUIDE_CLEAR_HIGHLIGHT':
                    log('📨 收到清除高亮请求');
                    window.guideHighlightService.clearHighlight();
                    // 回复成功消息
                    if (event.source) {
                        event.source.postMessage({
                            type: 'CLEAR_HIGHLIGHT_RESULT',
                            data: { success: true }
                        }, '*');
                    }
                    break;
                    
                default:
                    // 其他消息类型的处理
                    if (type && type.startsWith('GUIDE_')) {
                        log('📨 收到引导消息: ' + type);
                    }
            }
        });
        
        // 测试函数
        function testGuideId() {
            log('测试 data-guide-id 高亮...');
            window.guideHighlightService.highlightElement({
                guideId: 'create-api-button'
            }, {
                borderColor: '#52c41a',
                borderWidth: 3
            });
        }
        
        function testSelector() {
            log('测试 CSS 选择器高亮...');
            window.guideHighlightService.highlightElement({
                selector: '[data-guide-id="api-manage-menu"]'
            }, {
                borderColor: '#1890ff',
                borderWidth: 3
            });
        }
        
        function testText() {
            log('测试文本匹配高亮...');
            window.guideHighlightService.highlightElement({
                text: '创建API'
            }, {
                borderColor: '#faad14',
                borderWidth: 3
            });
        }
        
        function testMarkdownLink() {
            log('模拟 Markdown 链接点击...');
            // 模拟从微应用发送的消息
            window.postMessage({
                type: 'GUIDE_HIGHLIGHT',
                target: { guideId: 'api-manage-menu' },
                options: { borderColor: '#722ed1', borderWidth: 3 }
            }, '*');
        }
        
        function clearHighlight() {
            log('清除高亮...');
            window.guideHighlightService.clearHighlight();
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            log('🎯 页面加载完成，高亮服务已就绪');
        });
    </script>
</body>
</html>
