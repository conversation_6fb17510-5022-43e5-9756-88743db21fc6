/**
 * 整个应用构建, 编译配置
 *
 * 详细工程化相关配置 详情参见：
 * https://xconsole.aliyun-inc.com/nexconsole/develop/config
 */
import { XConsolePresetConfig } from '@ali/xconsole/types';
import { resolve } from 'path';

const config: XConsolePresetConfig = {
  // 产品名称, 如果是控制台项目一定要配置此选项
  // 保持和你 viper code 相关
  appType: 'widget',

  appId: '@ali/alfa-cloud-apigateway-widget-document-quickstart',

  consoleBase: false,

  disablePolyfill: true,

  disableErrorOverlay: true,

  useTerserPlugin: true,

  htmlFileName: resolve(__dirname, '../public/index.html'),

  webpack(config) {
    return config;
  }
};


module.exports = config;
