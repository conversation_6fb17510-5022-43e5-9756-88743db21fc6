// 引导系统类型定义
export interface GuideConfig {
  guides: Guide[];
  categories: Category[];
}

export interface Guide {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: string;
  icon?: string;
  tags: string[];
  prerequisites: Prerequisites;
  steps: Step[];
  completion: Completion;
}

export interface Prerequisites {
  type: 'static' | 'interactive';
  configFile?: string;
  contentFile: string;
}

export interface Step {
  id: string;
  title: string;
  contentFile: string;
  target?: string;
  action?: StepAction;
  validation?: StepValidation;
  beforeNext?: string;
}

export interface Completion {
  contentFile: string;
  nextGuides?: string[];
}

export interface Category {
  id: string;
  name: string;
  description: string;
  order: number;
}

export interface InteractiveField {
  id: string;
  type: 'select' | 'input' | 'checkbox' | 'radio';
  label: string;
  required: boolean;
  placeholder?: string;
  dependsOn?: string;
  dataSource?: DataSource;
  validation?: FieldValidation;
}

export interface DataSource {
  type: 'api' | 'static';
  url?: string;
  data?: any[];
  params?: Record<string, string>;
  labelField: string;
  valueField: string;
}

export interface FieldValidation {
  required?: string;
  pattern?: string;
  custom?: (value: any) => string | null;
}

export interface StepAction {
  type: 'click' | 'hover' | 'input' | 'scroll' | 'wait' | 'none';
  target?: string;
  data?: any;
}

export interface StepValidation {
  type: 'url' | 'element' | 'form' | 'custom';
  pattern?: string;
  selector?: string;
  field?: string;
  required?: boolean;
  validator?: () => boolean;
}

export interface HighlightOptions {
  padding?: number;
  borderRadius?: number;
  animation?: 'pulse' | 'bounce' | 'none';
  maskColor?: string;
  zIndex?: number;
}

export interface NavigationOptions {
  replace?: boolean;
  preserveQuery?: boolean;
}

export interface ScrollOptions {
  behavior?: 'smooth' | 'instant';
  block?: 'start' | 'center' | 'end' | 'nearest';
  inline?: 'start' | 'center' | 'end' | 'nearest';
}

// 事件类型
export interface GuideEvent {
  type: string;
  data: any;
}

export interface GuideState {
  currentGuideId?: string;
  currentStepIndex: number;
  context: Record<string, any>;
  isVisible: boolean;
}

// 组件Props类型
export interface GuidePanelProps {
  visible: boolean;
  position: 'right' | 'left';
  width?: number;
  currentStep: number;
  steps: Step[];
  onNext: () => void;
  onPrev: () => void;
  onClose: () => void;
}

export interface MarkdownRendererProps {
  content: string;
  context?: Record<string, any>;
  onComponentEvent?: (event: GuideEvent) => void;
}

export interface GuideOverlayProps {
  visible: boolean;
  target?: string;
  options?: HighlightOptions;
  onClose?: () => void;
} 