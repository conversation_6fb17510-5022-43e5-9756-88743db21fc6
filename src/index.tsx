import React from 'react';
import CardExample from './components/CardExample';
import GuidePanel from './components/GuidePanel';
import TestGuidePanel from './components/TestGuidePanel';
import { AppName } from './types/interface';

export default (props: any) => {
  const components = {
    [AppName.startCard]: <CardExample />,
    [AppName.guidePanel]: <GuidePanel visible {...props} />,
    'testGuidePanel': <TestGuidePanel />,
  };

  return components[props?.appName] || <TestGuidePanel />;
};
