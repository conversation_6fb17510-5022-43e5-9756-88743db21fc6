import { GuideConfig } from '../types/guide';

// 示例引导配置
export const SAMPLE_GUIDE_CONFIG: GuideConfig = {
  guides: [
    {
      id: 'api-creation-guide',
      title: '创建API指南',
      description: '一步步教您如何创建您的第一个API',
      category: 'getting-started',
      difficulty: 'beginner',
      estimatedTime: '5分钟',
      icon: 'api',
      tags: ['API', '快速开始', '基础'],
      prerequisites: {
        type: 'interactive',
        configFile: 'guides/api-creation/prerequisites.json',
        contentFile: 'guides/api-creation/prerequisites.md'
      },
      steps: [
        {
          id: 'step-1',
          title: '进入API管理页面',
          contentFile: 'guides/api-creation/steps/step-1.md',
          target: '[data-guide-id="api-manage-menu"]',
          action: { type: 'click' }
        },
        {
          id: 'step-2',
          title: '创建新API',
          contentFile: 'guides/api-creation/steps/step-2.md',
          target: '[data-guide-id="create-api-button"]',
          action: { type: 'click' }
        }
      ],
      completion: {
        contentFile: 'guides/api-creation/completion.md',
        nextGuides: ['api-test-guide']
      }
    }
  ],
  categories: [
    {
      id: 'getting-started',
      name: '快速开始',
      description: '帮助新用户快速上手',
      order: 1
    }
  ]
};