import React, { useState, useEffect } from 'react';
import MarkdownRenderer from '../MarkdownRenderer';
import { Guide, GuideEvent, HighlightOptions } from '../../types/guide';
import { guideEventBus } from '../../services/GuideEventBus';
import './index.less';

export interface GuidePanelProps {
  /** 引导配置数据 */
  guide: Guide;
  /** 当前步骤索引 - 可选，不提供时使用内部状态管理 */
  currentStepIndex?: number;
  /** 是否显示面板 - 可选，不提供时使用内部状态管理 */
  visible?: boolean;
  /** 面板位置 */
  position?: 'right' | 'left';
  /** 面板宽度 */
  width?: number;
  /** 上下文数据 */
  context?: Record<string, any>;
  /** 下一步回调 - 可选，不提供时使用内部状态管理 */
  onNext?: () => void;
  /** 上一步回调 - 可选，不提供时使用内部状态管理 */
  onPrev?: () => void;
  /** 关闭回调 - 可选，会在内部关闭后调用 */
  onClose?: () => void;
  /** 完成回调 - 可选，会在内部完成后调用 */
  onComplete?: () => void;
  /** 步骤变化回调 */
  onStepChange?: (stepIndex: number) => void;
  /** 显示状态变化回调 */
  onVisibilityChange?: (visible: boolean) => void;
}

interface ComponentState {
  stepContent: string;
  loading: boolean;
  error: string | null;
  highlighted: boolean;
  currentStepIndex: number;
  visible: boolean;
}

const GuidePanel: React.FC<GuidePanelProps> = ({
  guide,
  currentStepIndex,
  visible,
  position = 'right',
  width = 400,
  context = {},
  onNext,
  onPrev,
  onClose,
  onComplete,
  onStepChange,
  onVisibilityChange,
}) => {
  const [state, setState] = useState<ComponentState>({
    stepContent: '',
    loading: false,
    error: null,
    highlighted: false,
    currentStepIndex: currentStepIndex || 0,
    visible: visible !== undefined ? visible : true,
  });

  // 当外部 currentStepIndex 变化时同步内部状态
  useEffect(() => {
    if (currentStepIndex !== undefined && currentStepIndex !== state.currentStepIndex) {
      setState(prev => ({
        ...prev,
        currentStepIndex,
      }));
    }
  }, [currentStepIndex]);

  // 当外部 visible 变化时同步内部状态
  useEffect(() => {
    if (visible !== undefined && visible !== state.visible) {
      setState(prev => ({
        ...prev,
        visible,
      }));
    }
  }, [visible]);

  // 计算当前步骤信息
  const currentStep = guide?.steps?.[state.currentStepIndex];
  const isFirstStep = state.currentStepIndex === 0;
  console.log('isFirstStep', isFirstStep);
  const isLastStep = state.currentStepIndex === (guide?.steps?.length || 0) - 1;
  const progress = guide?.steps?.length ? ((state.currentStepIndex + 1) / guide.steps.length) * 100 : 0;
  const total = guide?.steps?.length || 0;

  // 计算面板样式
  const panelStyle = {
    [position]: 0,
    width,
    transform: state.visible ? 'translateX(0)' : `translateX(${position === 'right' ? '100%' : '-100%'})`,
  };

  // 内部 visible 管理方法
  const setInternalVisible = (newVisible: boolean) => {
    setState(prev => ({
      ...prev,
      visible: newVisible,
    }));

    // 通知外部
    onVisibilityChange?.(newVisible);
  };

  // 加载步骤内容
  const loadStepContent = async () => {
    if (!currentStep?.contentFile) {
      setState(prev => ({ ...prev, stepContent: '', loading: false, error: null }));
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const content = await loadContentFromFile(currentStep.contentFile, context);
      setState(prev => ({
        ...prev,
        stepContent: content,
        loading: false,
        error: null,
      }));
    } catch (error) {
      console.error('Failed to load step content:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: '无法加载步骤内容，请稍后重试。',
      }));
    }
  };

  // 高亮目标元素
  const highlightTargetElement = () => {
    console.log("====22222====",currentStep)
    if (!currentStep?.target) {
      guideEventBus.emit('guide:clearHighlight');
      setState(prev => ({ ...prev, highlighted: false }));
      return;
    }

    const highlightOptions: HighlightOptions = {
      padding: 8,
      borderRadius: 4,
      animation: 'pulse',
    };

    guideEventBus.emit('guide:highlight', {
      target: currentStep.target,
      options: highlightOptions,
    });

    setState(prev => ({ ...prev, highlighted: true }));
  };

  // 清除高亮
  const clearHighlight = () => {
    guideEventBus.emit('guide:clearHighlight');
    setState(prev => ({ ...prev, highlighted: false }));
  };

  // 处理组件事件
  const handleComponentEvent = (event: GuideEvent) => {
    console.log('Guide component event:', event);

    switch (event.type) {
      case 'buttonClick':
        if (event.data?.action === 'click') {
          // 执行点击操作后自动进入下一步
          setTimeout(() => {
            handleNext();
          }, 1000);
        }
        break;

      case 'linkClick':
        // 发送导航事件到主应用
        guideEventBus.emit('guide:navigate', event.data);
        break;

      case 'formSubmit':
        // 处理表单提交
        if (event.data?.valid) {
          handleNext();
        }
        break;

      default:
        // 传递未处理的事件到主应用
        guideEventBus.emit('guide:componentEvent', event);
    }
  };

  // 导航处理函数
  const handleNext = () => {
    if (isLastStep) {
      // 完成引导
      onComplete && onComplete();
      setInternalVisible(false);
    } else {
      const nextIndex = state.currentStepIndex + 1;

      // 如果有外部控制，调用外部回调
      if (onNext) {
        onNext();
      } else {
        // 内部状态管理
        setState(prev => ({
          ...prev,
          currentStepIndex: nextIndex,
        }));
      }

      // 通知步骤变化
      onStepChange?.(nextIndex);
    }
  };

  const handlePrev = () => {
    if (!isFirstStep) {
      const prevIndex = state.currentStepIndex - 1;

      // 如果有外部控制，调用外部回调
      if (onPrev) {
        onPrev();
      } else {
        // 内部状态管理
        setState(prev => ({
          ...prev,
          currentStepIndex: prevIndex,
        }));
      }

      // 通知步骤变化
      onStepChange?.(prevIndex);
    }
  };

  const handleClose = () => {
    clearHighlight();
    onClose && onClose();
    setInternalVisible(false);
  };

  // 键盘事件处理
  const handleKeyDown = (event: KeyboardEvent) => {
    if (!state.visible) return;

    switch (event.key) {
      case 'Escape':
        handleClose();
        break;
      case 'ArrowRight':
      case 'Enter':
        if (!isLastStep) {
          event.preventDefault();
          handleNext();
        }
        break;
      case 'ArrowLeft':
        if (!isFirstStep) {
          event.preventDefault();
          handlePrev();
        }
        break;
      default:
        break;
    }
  };

  // 副作用处理
  useEffect(() => {
    loadStepContent();
  }, [state.currentStepIndex, guide]);

  useEffect(() => {
    highlightTargetElement();
    return () => clearHighlight();
  }, [currentStep]);

  useEffect(() => {
    if (state.visible) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
    return undefined;
  }, [state.visible, isFirstStep, isLastStep]);

  // 生命周期清理
  useEffect(() => {
    return () => {
      clearHighlight();
    };
  }, []);

  if (!state.visible || !guide) {
    return null;
  }

  return (
    <div
      className={`guide-panel guide-panel--${position}`}
      style={panelStyle}
      role="dialog"
      aria-labelledby="guide-panel-title"
      aria-modal="true"
    >
      {/* 头部 */}
      <header className="guide-panel-header">
        <div className="guide-info">
          <h2 id="guide-panel-title">{guide.title}</h2>
          <div className="step-indicator">
            步骤 {state.currentStepIndex + 1} / {total}
          </div>
        </div>
        <div className="guide-actions">
          <button
            className="guide-btn guide-btn--text"
            onClick={handleClose}
            aria-label="关闭引导"
            title="关闭引导"
          >
            ✕
          </button>
        </div>
      </header>

      {/* 进度条 */}
      <div className="guide-panel-progress">
        <div
          className="progress-bar"
          style={{ width: `${progress}%` }}
          role="progressbar"
          aria-valuenow={progress}
          aria-valuemin={0}
          aria-valuemax={100}
          aria-label={`引导进度: ${Math.round(progress)}%`}
        />
      </div>

      {/* 内容区域 */}
      <main className="guide-panel-content">
        <div className="step-title">
          <h3>{currentStep?.title}</h3>
        </div>

        <div className="step-content">
          {state.loading && (
            <div className="loading-placeholder" role="status" aria-live="polite">
              <div className="loading-spinner" />
              <span>加载中...</span>
            </div>
          )}

          {state.error && (
            <div className="error-placeholder" role="alert">
              <div className="error-icon">⚠️</div>
              <p>{state.error}</p>
              <button
                className="guide-btn guide-btn--secondary"
                onClick={loadStepContent}
              >
                重试
              </button>
            </div>
          )}

          {!state.loading && !state.error && state.stepContent && (
            <MarkdownRenderer
              content={state.stepContent}
              context={context}
              onComponentEvent={handleComponentEvent}
            />
          )}
        </div>
      </main>

      {/* 底部导航 */}
      <footer className="guide-panel-footer">
        <div className="navigation-buttons">
          <button
            className="guide-btn guide-btn--secondary"
            onClick={handlePrev}
            disabled={isFirstStep}
            aria-label="上一步"
          >
            上一步
          </button>

          <button
            className="guide-btn guide-btn--primary"
            onClick={handleNext}
            aria-label={isLastStep ? '完成引导' : '下一步'}
          >
            {isLastStep ? '完成' : '下一步'}
          </button>
        </div>

        {/* 额外操作 */}
        <div className="guide-meta">
          <span className="time-estimate">
            预计时间: {guide.estimatedTime}
          </span>
          {guide.difficulty && (
            <span className={`difficulty difficulty--${guide.difficulty}`}>
              {getDifficultyText(guide.difficulty)}
            </span>
          )}
        </div>
      </footer>
    </div>
  );
};

// 辅助函数
async function loadContentFromFile(
  filename: string,
  context: Record<string, any>,
): Promise<string> {
  // 模拟加载内容，实际项目中这里应该是真实的文件加载逻辑
  await new Promise(resolve => setTimeout(resolve, 300));

  // 根据文件名返回不同的示例内容
  if (filename.includes('step-1')) {
    return `# API创建快速指南

欢迎使用API网关管理控制台！本指南将帮助您快速创建您的第一个API。

## 步骤1：进入API管理页面

首先，您需要进入API管理页面。
[创建API](highlight_text "API管理") 
您也可以通过链接方式：<GuideLink target="API管理">API管理</GuideLink> 菜单。

## 步骤2：创建新API

在API管理页面中，找到创建API的按钮。

<guide-button
  action="highlight"
  type="highlight"
  text="高亮创建API按钮"
  highlight-target="create-api-button"
/>

或者点击链接：[创建API](guide:highlight:create-api-button) 按钮开始创建您的第一个API。

## 步骤3：配置API基本信息

在创建API的表单中，您需要填写以下基本信息：

- **API名称**：为您的API起一个有意义的名称
- **API路径**：定义API的访问路径
- **请求方法**：选择HTTP方法（GET、POST、PUT、DELETE等）
- **描述**：简要描述API的功能

## 步骤4：配置后端服务

配置API对应的后端服务地址和参数：

- **后端地址**：您的实际服务地址
- **超时时间**：设置请求超时时间
- **重试次数**：设置失败重试次数

## 步骤5：测试API

创建完成后，您可以：

1. 查看现有API列表中的新API

<guide-button
  action="highlight"
  type="highlight"
  text="高亮第一个API项目"
  highlight-target="api-item-1"
/>

2. 点击编辑按钮进行修改
3. 使用API测试工具验证功能

## 🎯 快速操作面板

<guide-button
  action="highlight"
  type="highlight"
  text="🎯 高亮控制台首页"
  highlight-target="dashboard-menu"
/>

<guide-button
  action="highlight"
  type="highlight"
  text="🎯 高亮网关管理"
  highlight-target="gateway-menu"
/>

<guide-button
  action="highlight"
  type="highlight"
  text="🎯 高亮监控中心"
  highlight-target="monitor-menu"
/>

<guide-button
  action="clearHighlight"
  type="secondary"
  text="🧹 清除所有高亮"
/>

## 常见问题

### Q: 如何修改已创建的API？
A: 在API列表中找到对应的API，点击编辑按钮即可修改。

### Q: API创建后多久生效？
A: API创建后会立即生效，您可以马上开始使用。

### Q: 如何删除不需要的API？
A: 在API详情页面中，您可以找到删除选项。

## 下一步

恭喜！您已经成功创建了第一个API。接下来您可以：

- 学习如何配置API的安全策略
- 了解API的监控和日志功能
- 探索更多高级功能

---

💡 **提示**：如果您在使用过程中遇到任何问题，可以随时查看帮助文档或联系技术支持。
`;
  }

  if (filename.includes('step-2')) {
    return `# 步骤2: 创建新API

现在您已经进入了API管理页面，接下来我们来创建一个新的API。

## 操作说明

1. 点击页面右上角的 **"创建API"** 按钮
2. 在弹出的对话框中选择API类型

## API类型说明

根据您的业务需求选择合适的API类型：

### HTTP API ⭐ *推荐新手使用*
- **适用场景**：REST风格的Web API
- **特点**：简单易用，支持完整的HTTP方法
- **示例**：用户管理API、订单查询API

### WebSocket API
- **适用场景**：实时通信、推送服务
- **特点**：双向通信，低延迟
- **示例**：在线聊天、实时监控

## 🎯 交互操作

<guide-button
  action="highlight"
  type="highlight"
  text="🎯 高亮创建API按钮"
  highlight-target="create-api-button"
/>

<guide-button
  action="highlight"
  type="highlight"
  text="🎯 高亮API管理菜单"
  highlight-target="api-manage-menu"
/>

<guide-button
  action="clearHighlight"
  type="secondary"
  text="🧹 清除高亮"
/>

---

*选择API类型后，我们将进入基本信息填写*`;
  }

  return `# ${filename}

这是示例步骤内容。在实际项目中，这里会从 ${filename} 加载具体的Markdown文件。

当前上下文: ${JSON.stringify(context, null, 2)}`;
}

function getDifficultyText(difficulty: string): string {
  const difficultyMap: Record<string, string> = {
    beginner: '入门',
    intermediate: '中级',
    advanced: '高级',
  };
  return difficultyMap[difficulty] || difficulty;
}

export default GuidePanel;