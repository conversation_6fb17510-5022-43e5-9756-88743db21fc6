/* 引导面板样式 */
.guide-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  z-index: 10001;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  transition: transform 0.3s ease;

  &--left {
    left: 0;
    right: auto;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  }

  @media (max-width: 768px) {
    width: 100%;
    height: 60vh;
    top: auto;
    bottom: 0;
  }
}

.guide-panel-header {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;

  .guide-info {
    flex: 1;

    h2 {
      margin: 0 0 0.25rem 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #333;
    }

    .step-indicator {
      font-size: 0.875rem;
      color: #6c757d;
    }
  }

  .guide-actions {
    display: flex;
    gap: 0.5rem;
  }
}

.guide-panel-progress {
  height: 4px;
  background: #e9ecef;

  .progress-bar {
    height: 100%;
    background: #007bff;
    transition: width 0.3s ease;
  }
}

.guide-panel-content {
  flex: 1;
  overflow: auto;
  padding: 1rem;

  .step-title {
    margin-bottom: 1rem;

    h3 {
      margin: 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: #333;
    }
  }

  .step-content {
    flex: 1;
  }

  .loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6c757d;
    font-size: 0.875rem;
    gap: 0.5rem;

    .loading-spinner {
      width: 24px;
      height: 24px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #007bff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  .error-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #dc3545;
    font-size: 0.875rem;
    gap: 0.75rem;
    text-align: center;

    .error-icon {
      font-size: 2rem;
    }

    p {
      margin: 0;
      color: #6c757d;
    }
  }
}

.guide-panel-footer {
  padding: 1rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;

  .navigation-buttons {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    margin-bottom: 0.75rem;
  }

  .guide-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: #6c757d;

    .time-estimate {
      color: #6c757d;
    }

    .difficulty {
      padding: 0.125rem 0.5rem;
      border-radius: 0.25rem;
      font-weight: 500;

      &--beginner {
        background: #d4edda;
        color: #155724;
      }

      &--intermediate {
        background: #fff3cd;
        color: #856404;
      }

      &--advanced {
        background: #f8d7da;
        color: #721c24;
      }
    }
  }
}

/* 引导按钮样式 */
.guide-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &--primary {
    background: #007bff;
    color: white;

    &:hover:not(:disabled) {
      background: #0056b3;
    }
  }

  &--secondary {
    background: #6c757d;
    color: white;

    &:hover:not(:disabled) {
      background: #545b62;
    }
  }

  &--text {
    background: transparent;
    color: #6c757d;
    padding: 0.25rem 0.5rem;

    &:hover:not(:disabled) {
      color: #333;
      background: rgba(0, 0, 0, 0.05);
    }
  }
}

/* 动画效果 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .guide-panel {
    width: 350px;
  }
}

@media (max-width: 768px) {
  .guide-panel {
    width: 100%;
    height: 50vh;
    top: auto;
    bottom: 0;
    border-radius: 1rem 1rem 0 0;
  }

  .guide-panel-header {
    padding: 0.75rem 1rem;
  }

  .guide-panel-content {
    padding: 0.75rem 1rem;
  }

  .guide-panel-footer {
    padding: 0.75rem 1rem;

    .navigation-buttons {
      flex-direction: column;
      gap: 0.5rem;
    }

    .guide-meta {
      flex-direction: column;
      gap: 0.5rem;
      align-items: flex-start;
    }
  }
} 