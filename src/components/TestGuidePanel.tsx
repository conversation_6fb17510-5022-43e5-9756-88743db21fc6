import React, { useState } from 'react';
import GuidePanel from './GuidePanel';
import { Guide } from '../types/guide';

const TestGuidePanel: React.FC = () => {
  const [visible, setVisible] = useState(true);

  // 测试引导数据
  const testGuide: Guide = {
    id: 'test-api-creation',
    title: 'API创建引导',
    description: '学习如何创建您的第一个API',
    category: 'api-management',
    difficulty: 'beginner',
    estimatedTime: '5分钟',
    tags: ['API', '创建', '入门'],
    prerequisites: {
      type: 'static',
      contentFile: 'prerequisites.md'
    },
    steps: [
      {
        id: 'step-1',
        title: '进入API管理页面',
        contentFile: 'step-1.md',
        target: 'api-manage-menu'
      },
      {
        id: 'step-2', 
        title: '创建新API',
        contentFile: 'step-2.md',
        target: 'create-api-button'
      }
    ],
    completion: {
      contentFile: 'completion.md'
    }
  };

  return (
    <div style={{ position: 'relative', height: '100vh' }}>
      <div style={{ padding: '20px' }}>
        <h2>引导面板测试</h2>
        <button 
          onClick={() => setVisible(!visible)}
          style={{ 
            padding: '10px 20px', 
            backgroundColor: '#1890ff', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          {visible ? '隐藏' : '显示'}引导面板
        </button>
      </div>

      {visible && (
        <GuidePanel
          guide={testGuide}
          visible={visible}
          position="right"
          width={400}
          onClose={() => setVisible(false)}
          onComplete={() => {
            console.log('引导完成!');
            setVisible(false);
          }}
          onStepChange={(stepIndex) => {
            console.log('步骤变化:', stepIndex);
          }}
        />
      )}
    </div>
  );
};

export default TestGuidePanel;
