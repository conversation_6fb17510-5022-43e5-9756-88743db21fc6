import React, { useState, useEffect, useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { MarkdownRendererProps } from '../../types/guide';
import { guideEventBus } from '../../services/GuideEventBus';
import GuideSelect from './components/GuideSelect';
import GuideButton from './components/GuideButton';
import GuideLink from './components/GuideLink';
import './index.less';

/**
 * 增强的Markdown处理器
 */
class EnhancedMarkdownProcessor {
  private context: Record<string, any> = {};

  constructor(context: Record<string, any> = {}) {
    this.context = context;
  }

  /**
   * 处理模板变量
   */
  processTemplate(content: string): string {
    return content.replace(/\{\{([^}]+)\}\}/g, (match, variable) => {
      const value = this.getNestedValue(this.context, variable.trim());
      return value !== undefined ? String(value) : match;
    });
  }

  /**
   * 获取嵌套对象值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * 解析自定义标签
   */
  parseCustomTags(content: string): string {
    console.log('🔍 parseCustomTags input:', content);

    // 处理自闭合标签 - 先转换为标准标签格式
    const selfClosingTagRegex = /<guide-(\w+)([^>]*?)\/>/g;
    content = content.replace(selfClosingTagRegex, (match, tagName, attributes) => {
      console.log('🔧 Found self-closing tag:', { match, tagName, attributes });
      return `<guide-${tagName}${attributes}></guide-${tagName}>`;
    });

    // 处理标准标签 - 转换为组件占位符
    const customTagRegex = /<guide-(\w+)([^>]*?)>(.*?)<\/guide-\1>/gs;
    const result = content.replace(customTagRegex, (match, tagName, attributes, children) => {
      console.log('🔧 Found standard tag:', { match, tagName, attributes, children });
      const props = this.parseAttributes(attributes);
      console.log('🔧 Parsed props:', props);

      // 创建组件数据
      const componentData = {
        tagName,
        props,
        children: children.trim()
      };

      const placeholder = `<!--GUIDE-COMPONENT:${JSON.stringify(componentData)}-->`;
      console.log('🔧 Created placeholder:', placeholder);
      return placeholder;
    });

    console.log('🔍 parseCustomTags output:', result);
    return result;
  }

  /**
   * 解析HTML属性
   */
  private parseAttributes(attributeString: string): Record<string, any> {
    const attrs: Record<string, any> = {};
    // 支持带引号和不带引号的属性值
    const attrRegex = /(\w+(?:-\w+)*)=(?:["']([^"']*)["']|([^\s>]+))/g;

    console.log('🔧 Parsing attributes:', attributeString);

    let match;
    while ((match = attrRegex.exec(attributeString)) !== null) {
      const [, key, quotedValue, unquotedValue] = match;
      const value = quotedValue || unquotedValue;
      const camelKey = this.camelCase(key);
      attrs[camelKey] = value;
      console.log('🔧 Attribute parsed:', { key, camelKey, value });
    }

    console.log('🔧 Final attributes:', attrs);
    return attrs;
  }

  /**
   * 转换为驼峰命名
   */
  private camelCase(str: string): string {
    return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
  }


}

/**
 * 自定义组件映射
 */
const customComponents = {
  'guide-select': GuideSelect,
  'guide-button': GuideButton,
  'guide-link': GuideLink,
};

/**
 * Markdown渲染器组件
 */
const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  context = {},
  onComponentEvent
}) => {
  const [processedContent, setProcessedContent] = useState('');
  const [components, setComponents] = useState<React.ReactNode[]>([]);

  const processor = useMemo(() => new EnhancedMarkdownProcessor(context), [context]);

  useEffect(() => {
    // 处理模板变量
    let processed = processor.processTemplate(content);
    
    // 处理自定义标签
    processed = processor.parseCustomTags(processed);
    console.log("00000000",processed)
    setProcessedContent(processed);
  }, [content, context, processor]);

  // 自定义渲染器组件
  const rendererComponents = {
    // 代码高亮
    code({ node, inline, className, children, ...props }: any) {
      const match = /language-(\w+)/.exec(className || '');
      return !inline && match ? (
        <SyntaxHighlighter
          style={tomorrow}
          language={match[1]}
          PreTag="div"
          {...props}
        >
          {String(children).replace(/\n$/, '')}
        </SyntaxHighlighter>
      ) : (
        <code className={className} {...props}>
          {children}
        </code>
      );
    },

    // 处理链接
    a({ href, children, ...props }: any) {
      console.log('🔗 ======:', href, children);
      // 检查是否是特殊的引导链接
      if (href?.startsWith('guide:')) {
        const parts = href.split(':');
        const action = parts[1];
        const target = parts.slice(2).join(':'); // 支持目标中包含冒号的情况

        console.log('🔗 解析引导链接:', { href, action, target });

        return (
          <GuideLink
            action={action}
            target={target}
            href={href}
            onEvent={onComponentEvent}
            {...props}
          >
            {children}
          </GuideLink>
        );
      }

      // 外部链接在新窗口打开
      if (href?.startsWith('http')) {
        return (
          <a href={href} target="_blank" rel="noopener noreferrer" {...props}>
            {children}
          </a>
        );
      }

      return <a href={href} {...props}>{children}</a>;
    },

    // 处理HTML内容（包含自定义组件）
    html({ children, ...props }: any) {
      const htmlContent = String(children);
      console.log('🔍 HTML content:', htmlContent);

      // 检查是否包含组件占位符
      const componentRegex = /<!--GUIDE-COMPONENT:(.*?)-->/g;
      const parts = [];
      let lastIndex = 0;
      let match;

      while ((match = componentRegex.exec(htmlContent)) !== null) {
        // 添加组件前的文本
        if (match.index > lastIndex) {
          parts.push(htmlContent.slice(lastIndex, match.index));
        }

        // 解析并渲染组件
        try {
          const componentData = JSON.parse(match[1]);
          const Component = customComponents[`guide-${componentData.tagName}` as keyof typeof customComponents];
          
          if (Component) {
            parts.push(
              <Component
                key={match.index}
                {...componentData.props}
                onEvent={onComponentEvent}
              >
                {componentData.children}
              </Component>
            );
          }
        } catch (error) {
          console.error('Failed to parse component data:', error);
          parts.push(match[0]);
        }

        lastIndex = match.index + match[0].length;
      }

      // 添加最后的文本
      if (lastIndex < htmlContent.length) {
        parts.push(htmlContent.slice(lastIndex));
      }

      return parts.length > 1 ? <>{parts}</> : <div dangerouslySetInnerHTML={{ __html: htmlContent }} />;
    }
  };

  return (
    <div className="guide-markdown-renderer">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        components={rendererComponents}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer; 