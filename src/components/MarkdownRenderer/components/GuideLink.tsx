import React from 'react';
import { guideEventBus } from '../../../services/GuideEventBus';

interface GuideLinkProps {
  href?: string;
  action?: string;
  target?: string;
  children: React.ReactNode;
  onEvent?: (event: any) => void;
}

const GuideLink: React.FC<GuideLinkProps> = ({
  href = '#',
  action = 'navigate',
  target = '_self',
  children,
  onEvent,
}) => {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();

    console.log('🔗 GuideLink clicked:', { href, action, target });

    switch (action) {
      case 'navigate':
        guideEventBus.navigate(href || '#', target === '_blank');
        break;
      case 'highlight':
        // 解析目标字符串，支持多种格式
        const highlightTarget = href || target || '';
        console.log('🎯 高亮目标:', highlightTarget);
        guideEventBus.highlight(highlightTarget);
        break;
      case 'scroll':
        // 滚动功能暂时使用组件事件
        guideEventBus.componentEvent({
          type: 'scroll',
          data: { target: href },
        });
        break;
      default:
        guideEventBus.componentEvent({
          type: 'linkClick',
          data: { href, action, target },
        });
    }

    // 发送本地事件
    onEvent?.({
      type: 'linkClick',
      data: { href, action, target },
    });
  };

  return (
    <a
      href={href}
      onClick={handleClick}
      className="guide-link"
      style={{
        color: '#1890ff',
        textDecoration: 'underline',
        cursor: 'pointer'
      }}
    >
      {children}
    </a>
  );
};

export default GuideLink; 