import React from 'react';
import { guideEventBus } from '../../../services/GuideEventBus';

interface GuideLinkProps {
  href?: string;
  action?: 'navigate' | 'highlight' | 'scroll';
  target?: '_blank' | '_self';
  children: React.ReactNode;
  onComponentEvent?: (event: any) => void;
}

const GuideLink: React.FC<GuideLinkProps> = ({
  href = '#',
  action = 'navigate',
  target = '_self',
  children,
  onComponentEvent,
}) => {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();

    switch (action) {
      case 'navigate':
        guideEventBus.navigate(href || '#', target === '_blank');
        break;
      case 'highlight':
        guideEventBus.highlight(href || '');
        break;
      case 'scroll':
        // 滚动功能暂时使用组件事件
        guideEventBus.componentEvent({
          type: 'scroll',
          data: { target: href },
        });
        break;
      default:
        guideEventBus.componentEvent({
          type: 'linkClick',
          data: { href, action, target },
        });
    }

    // 发送本地事件
    onComponentEvent?.({
        type: 'linkClick',
      data: { href, action, target },
      });
  };

  return (
    <a
      href={href}
      onClick={handleClick}
      className="guide-link"
      target={target}
    >
      {children}
    </a>
  );
};

export default GuideLink; 