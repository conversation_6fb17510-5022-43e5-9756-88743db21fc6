import React from 'react';
import { guideEventBus } from '../../../services/GuideEventBus';

interface GuideButtonProps {
  action: string;
  text: string;
  type?: 'primary' | 'secondary' | 'normal' | 'highlight';
  disabled?: boolean | string;
  target?: string;
  data?: any;
  onEvent?: (event: any) => void;
  children?: React.ReactNode;
  // 高亮相关属性
  highlightTarget?: string;
  highlightOptions?: {
    borderColor?: string;
    borderWidth?: number;
    padding?: number;
    animation?: string;
  };
}

const GuideButton: React.FC<GuideButtonProps> = ({
  action,
  text,
  type = 'primary',
  disabled = false,
  target,
  data,
  onEvent,
  children,
  highlightTarget,
  highlightOptions = {}
}) => {
  const handleClick = () => {
    if (typeof disabled === 'string') {
      // 简单的表达式计算（实际项目中应该用更安全的方式）
      try {
        const isDisabled = eval(disabled);
        if (isDisabled) return;
      } catch {
        // 如果表达式无效，默认不禁用
      }
    } else if (disabled) {
      return;
    }

    // 处理高亮动作
    if (action === 'highlight' || type === 'highlight') {
      const targetToHighlight = highlightTarget || target;
      if (targetToHighlight) {
        console.log('🎯 GuideButton 触发高亮:', targetToHighlight);

        // 解析目标格式
        let highlightTargetObj: any = {};

        if (targetToHighlight.startsWith('[') && targetToHighlight.includes('=')) {
          // CSS选择器格式: [data-guide-id="create-api-button"]
          highlightTargetObj = { selector: targetToHighlight };
        } else if (targetToHighlight.includes('-')) {
          // guide-id格式: create-api-button
          highlightTargetObj = { guideId: targetToHighlight };
        } else {
          // 文本匹配格式
          highlightTargetObj = { text: targetToHighlight };
        }

        // 发送高亮请求
        guideEventBus.highlight(highlightTargetObj, {
          borderColor: '#1890ff',
          borderWidth: 3,
          animation: 'pulse',
          ...highlightOptions
        });
      }
    } else if (action === 'clearHighlight') {
      // 清除高亮
      console.log('🧹 GuideButton 清除高亮');
      guideEventBus.clearHighlight();
    } else {
      // 其他动作的处理
      // 发送事件到主应用
      guideEventBus.componentEvent({
        type: 'buttonClick',
        data: {
          action,
          target,
          data
        }
      });

      // 发送本地事件
      guideEventBus.emit('buttonClick', {
        action,
        target,
        data
      });
    }

    // 通知父组件
    if (onEvent) {
      onEvent({
        type: 'buttonClick',
        data: { action, target, data, highlightTarget }
      });
    }
  };

  const isDisabled = typeof disabled === 'string' 
    ? (() => {
        try {
          return eval(disabled);
        } catch {
          return false;
        }
      })()
    : disabled;

  // 根据类型设置按钮样式
  const getButtonClass = () => {
    const baseClass = 'guide-button';
    const typeClass = `guide-button--${type}`;

    if (type === 'highlight') {
      return `${baseClass} ${typeClass} guide-button--highlight`;
    }

    return `${baseClass} ${typeClass}`;
  };

  return (
    <button
      className={getButtonClass()}
      onClick={handleClick}
      disabled={isDisabled}
      title={type === 'highlight' ? `高亮: ${highlightTarget || target}` : undefined}
    >
      {type === 'highlight' && '🎯 '}
      {text || children}
    </button>
  );
};

export default GuideButton; 