import React from 'react';
import { guideEventBus } from '../../../services/GuideEventBus';

interface GuideButtonProps {
  action: string;
  text: string;
  type?: 'primary' | 'secondary' | 'normal';
  disabled?: boolean | string;
  target?: string;
  data?: any;
  onEvent?: (event: any) => void;
  children?: React.ReactNode;
}

const GuideButton: React.FC<GuideButtonProps> = ({
  action,
  text,
  type = 'primary',
  disabled = false,
  target,
  data,
  onEvent,
  children
}) => {
  const handleClick = () => {
    if (typeof disabled === 'string') {
      // 简单的表达式计算（实际项目中应该用更安全的方式）
      try {
        const isDisabled = eval(disabled);
        if (isDisabled) return;
      } catch {
        // 如果表达式无效，默认不禁用
      }
    } else if (disabled) {
      return;
    }

    // 发送事件到主应用
    guideEventBus.componentEvent({
      type: 'buttonClick',
      data: {
      action,
      target,
      data
      }
    });

    // 发送本地事件
    guideEventBus.emit('buttonClick', {
      action,
      target,
      data
    });

    // 通知父组件
    if (onEvent) {
      onEvent({
        type: 'buttonClick',
        data: { action, target, data }
      });
    }
  };

  const isDisabled = typeof disabled === 'string' 
    ? (() => {
        try {
          return eval(disabled);
        } catch {
          return false;
        }
      })()
    : disabled;

  return (
    <button
      className={`guide-button guide-button--${type}`}
      onClick={handleClick}
      disabled={isDisabled}
    >
      {text || children}
    </button>
  );
};

export default GuideButton; 